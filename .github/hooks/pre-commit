#!/bin/sh
#
# Git pre-commit hook to run lint check and test coverage on entire repository
# Ensures code quality standards and test coverage before commits
#

echo "🔍 Running pre-commit checks..."

# Change to the code directory where package.json is located
cd code

# Run lint check on entire codebase
echo "🔍 Running lint check on entire repository..."
if ! pnpm lint; then
    echo ""
    echo "❌ Lint check failed!"
    echo "🔧 Please fix the linting errors above before committing."
    echo "💡 You can run 'pnpm lint --fix' to automatically fix some issues."
    echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ Lint check passed! Code quality looks good."

# Check if there are any staged TypeScript/JavaScript files that might need tests
staged_files=$(git diff --cached --name-only --diff-filter=AM | grep -E '\.(ts|tsx|js|jsx)$' | grep -v '\.test\.' | grep -v '\.spec\.' | head -10)

if [ -n "$staged_files" ]; then
    echo "🧪 Running tests to ensure coverage for new/modified code..."

    # Run tests to ensure they pass
    if ! pnpm test --passWithNoTests; then
        echo ""
        echo "❌ Tests failed!"
        echo "🔧 Please fix the failing tests before committing."
        echo "💡 You can run 'pnpm test' to see detailed test results."
        echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
        echo ""
        exit 1
    fi

    echo "✅ Tests passed!"

    # Check for potential missing test coverage (advisory only)
    echo "📊 Checking for potential test coverage gaps..."
    echo "📝 Modified/new files that may need test coverage:"
    echo "$staged_files" | while read -r file; do
        # Check if there's a corresponding test file
        base_name=$(echo "$file" | sed 's/\.[^.]*$//')
        test_file1="${base_name}.test.ts"
        test_file2="${base_name}.test.js"
        test_file3="${base_name}.spec.ts"
        test_file4="${base_name}.spec.js"

        if [ ! -f "$test_file1" ] && [ ! -f "$test_file2" ] && [ ! -f "$test_file3" ] && [ ! -f "$test_file4" ]; then
            echo "   ⚠️  $file (no corresponding test file found)"
        fi
    done

    echo ""
    echo "💡 Consider adding tests for files marked with ⚠️  if they contain new functionality."
    echo "📚 Test files should be named: filename.test.ts or filename.spec.ts"
else
    echo "📝 No TypeScript/JavaScript files modified - skipping test coverage check."
fi

# CI Simulation - Run the same commands that CI runs
echo ""
echo "🔄 Running CI simulation to catch integration issues..."

# Check if we have the required directories and commands
if [ -d "../docs/tech-specs" ] && command -v git >/dev/null 2>&1; then
    echo "🔍 Simulating sync-kg CI command..."

    # Run the same sync-kg command that CI runs (dry-run to avoid side effects)
    if ! pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs 2>/dev/null; then
        echo ""
        echo "❌ CI simulation failed!"
        echo "🔧 The sync-kg command that CI runs is failing."
        echo "💡 This means your changes would cause CI to fail."
        echo "🧪 To debug: pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs"
        echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
        echo ""
        exit 1
    fi

    echo "✅ CI simulation passed!"
else
    echo "⚠️  Skipping CI simulation (missing docs/tech-specs or git)"
fi

echo "✅ All pre-commit checks passed!"
exit 0

{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "test": {"dependsOn": ["build"]}, "test:fresh": {"dependsOn": ["build"], "cache": false}, "test:coverage": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "test:coverage:fresh": {"dependsOn": ["build"], "outputs": ["coverage/**"], "cache": false}, "type-check": {"dependsOn": []}, "lint": {"dependsOn": []}, "dev": {"cache": false, "persistent": true}}}
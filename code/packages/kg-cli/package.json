{"name": "@workflow-mapper/kg-cli", "version": "0.0.1", "description": "Knowledge graph CLI tool for building graphs from MDX specifications", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"build-kg": "./dist/build-kg.js", "sync-kg": "./dist/sync-kg.js"}, "scripts": {"build": "tsup src/index.ts src/build-kg.ts src/sync-kg.ts --format esm --dts", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage"}, "keywords": ["knowledge-graph", "cli", "mdx", "json-ld", "yaml"], "author": "WorkflowMapper Team", "license": "ISC", "dependencies": {"@workflow-mapper/code-parser-lib": "workspace:^", "@workflow-mapper/kg-sync-lib": "workspace:^", "@workflow-mapper/spec-parser-lib": "workspace:^", "commander": "^11.0.0", "glob": "^11.0.2", "simple-git": "^3.19.1", "yaml": "2.3.2"}, "devDependencies": {"@types/jest": "29.5.12", "@types/node": "20.12.7", "jest": "29.7.0", "ts-jest": "29.1.2", "tsup": "8.0.2", "typescript": "5.4.3"}}
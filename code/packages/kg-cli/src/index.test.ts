import { buildKnowledgeGraph } from './index.js';
import { writeFileSync, mkdirSync, rmSync, readFileSync } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

// Type definitions for JSON-LD structure
interface JsonLdNode {
  '@type': string;
  '@id'?: string;
  [key: string]: unknown;
}

interface JsonLdRelationship {
  '@type': 'Relationship';
  relationship: string;
  source?: string;
  target: string;
  confidence?: number;
  [key: string]: unknown;
}

interface JsonLdGraph {
  '@context': unknown;
  '@graph': JsonLdNode[];
}

describe('buildKnowledgeGraph', () => {
  const testDir = join(tmpdir(), 'kg-cli-test');

  beforeEach(() => {
    // Clean up and create test directory
    rmSync(testDir, { recursive: true, force: true });
    mkdirSync(testDir, { recursive: true });
  });

  afterEach(() => {
    // Clean up test directory
    rmSync(testDir, { recursive: true, force: true });
  });

  it('should build knowledge graph from MDX files', async () => {
    // Create test MDX files
    const milestoneContent = `---
title: Test Milestone
description: A test milestone
status: Draft
version: 1.0.0
tags: [milestone, test]
authors: [TestAuthor]
---

# Test Milestone

This is a test milestone.
`;

    const componentContent = `---
title: Test Component
description: A test component
status: Active
tags: [component]
implements: [test-milestone]
---

# Test Component

This component implements the test milestone.
`;

    mkdirSync(join(testDir, 'milestones'), { recursive: true });
    mkdirSync(join(testDir, 'components'), { recursive: true });

    writeFileSync(
      join(testDir, 'milestones', 'test-milestone.mdx'),
      milestoneContent
    );
    writeFileSync(
      join(testDir, 'components', 'test-component.mdx'),
      componentContent
    );

    const result = await buildKnowledgeGraph(testDir, { dryRun: true });

    expect(result.summary.specsCount).toBe(2);
    expect(result.summary.milestonesCount).toBe(1);
    expect(result.summary.componentsCount).toBe(1);
    expect(result.summary.relationshipsCount).toBe(1);
    expect(result.errors).toHaveLength(0);
    expect(result.files.jsonld).toBeUndefined(); // Dry run shouldn't create files
    expect(result.files.yaml).toBeUndefined();
  });

  it('should write files when not in dry run mode', async () => {
    const testContent = `---
title: Simple Test
description: A simple test file
---

# Simple Test
`;

    writeFileSync(join(testDir, 'simple.mdx'), testContent);

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
    });

    expect(result.files.jsonld).toBeDefined();
    expect(result.files.yaml).toBeDefined();

    // Verify files were actually written
    const jsonldPath = join(testDir, 'kg.jsonld');
    const yamlPath = join(testDir, 'kg.yaml');

    expect(() => readFileSync(jsonldPath, 'utf-8')).not.toThrow();
    expect(() => readFileSync(yamlPath, 'utf-8')).not.toThrow();

    // Verify content structure
    const jsonldContent = JSON.parse(readFileSync(jsonldPath, 'utf-8'));
    expect(jsonldContent['@context']).toBeDefined();
    expect(jsonldContent['@graph']).toBeDefined();
    expect(Array.isArray(jsonldContent['@graph'])).toBe(true);
  });

  it('should handle empty directory', async () => {
    const result = await buildKnowledgeGraph(testDir, { dryRun: true });

    expect(result.summary.specsCount).toBe(0);
    expect(result.summary.milestonesCount).toBe(0);
    expect(result.summary.componentsCount).toBe(0);
    expect(result.summary.relationshipsCount).toBe(0);
    expect(result.errors).toHaveLength(0);
  });

  it('should detect node types correctly', async () => {
    // Create files in different directories to test type detection
    const files = [
      {
        path: 'milestones/m1.mdx',
        content: '---\ntitle: Milestone 1\n---\n# M1',
      },
      {
        path: 'components/c1.mdx',
        content: '---\ntitle: Component 1\n---\n# C1',
      },
      { path: 'domains/d1.mdx', content: '---\ntitle: Domain 1\n---\n# D1' },
      { path: 'adrs/adr-001.mdx', content: '---\ntitle: ADR 1\n---\n# ADR' },
      {
        path: 'other/spec.mdx',
        content: '---\ntitle: Other Spec\n---\n# Spec',
      },
    ];

    for (const file of files) {
      const dir = join(testDir, file.path.split('/')[0]!);
      mkdirSync(dir, { recursive: true });
      writeFileSync(join(testDir, file.path), file.content);
    }

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
    });

    expect(result.errors).toHaveLength(0);

    // Read the generated JSON-LD to verify node types
    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    ) as JsonLdGraph;
    const nodes = jsonldContent['@graph'].filter(
      (item: JsonLdNode) => item['@type'] !== 'Relationship'
    );

    const nodeTypes = nodes.map((node: JsonLdNode) => node['@type']);
    expect(nodeTypes).toContain('Milestone');
    expect(nodeTypes).toContain('Component');
    expect(nodeTypes).toContain('Domain');
    expect(nodeTypes).toContain('ArchitecturalDecision');
    expect(nodeTypes).toContain('Specification');
  });

  it('should extract relationships from frontmatter', async () => {
    const specWithRelationships = `---
title: Spec with Relationships
implements: [target-spec-1, target-spec-2]
dependsOn: [dependency-spec]
---

# Spec with Relationships
`;

    writeFileSync(join(testDir, 'spec.mdx'), specWithRelationships);

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
    });

    expect(result.summary.relationshipsCount).toBe(3); // 2 implements + 1 dependsOn

    // Verify relationship structure in JSON-LD
    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    ) as JsonLdGraph;
    const relationships = jsonldContent['@graph'].filter(
      (item: JsonLdNode): item is JsonLdRelationship =>
        item['@type'] === 'Relationship'
    );

    expect(relationships).toHaveLength(3);

    const implementsRels = relationships.filter(
      (rel: JsonLdRelationship) => rel.relationship === 'implements'
    );
    const dependsOnRels = relationships.filter(
      (rel: JsonLdRelationship) => rel.relationship === 'dependsOn'
    );

    expect(implementsRels).toHaveLength(2);
    expect(dependsOnRels).toHaveLength(1);
  });

  it('should handle parsing errors gracefully', async () => {
    // Create a file with invalid YAML frontmatter
    const invalidContent = `---
title: Invalid File
invalid-yaml: [unclosed array
---
# Invalid File
`;

    writeFileSync(join(testDir, 'invalid.mdx'), invalidContent);

    const result = await buildKnowledgeGraph(testDir, { dryRun: true });

    expect(result.errors).toHaveLength(1);
    expect(result.errors[0]?.filePath).toContain('invalid.mdx');
    expect(result.summary.specsCount).toBe(0); // No specs successfully parsed
  });

  it('should process @implements annotations and create implements edges', async () => {
    // Create a spec file
    const specContent = `---
title: Test Milestone
description: A test milestone
version: 1.0.0
status: Draft
---

# Test Milestone M1.2

## Components

- AnnotationParser: Parses @implements annotations
`;

    writeFileSync(join(testDir, 'milestone.mdx'), specContent);

    // Mock annotations data
    const mockAnnotations = [
      {
        milestoneId: 'M1.2',
        componentName: 'AnnotationParser',
        functionName: 'parseAnnotations',
        filePath: 'src/parser.ts',
        confidence: 0.9,
        lastVerified: '2025-01-01T00:00:00Z',
        lineNumber: 10,
        errors: [],
      },
      {
        milestoneId: 'M1.2',
        componentName: 'ValidationEngine',
        functionName: 'validateData',
        filePath: 'src/validator.ts',
        confidence: 0.8,
        lastVerified: '2025-01-01T00:00:00Z',
        lineNumber: 15,
        errors: [],
      },
    ];

    // Mock code parse result with matching functions
    const mockCodeParseResult = {
      functions: [
        {
          id: 'function:12345678',
          name: 'parseAnnotations',
          signature: 'parseAnnotations(content: string): Annotation[]',
          file: 'src/parser.ts',
          line_start: 10,
          line_end: 20,
          lang: 'typescript' as const,
        },
        {
          id: 'function:87654321',
          name: 'validateData',
          signature: 'validateData(data: any): boolean',
          file: 'src/validator.ts',
          line_start: 15,
          line_end: 25,
          lang: 'typescript' as const,
        },
      ],
      calls: [],
      errors: [],
      stats: { filesProcessed: 2, functionsFound: 2, callsFound: 0 },
    };

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
      codeParseResult: mockCodeParseResult,
      annotations: mockAnnotations,
    });

    expect(result.errors).toHaveLength(0);

    // Read the generated JSON-LD to verify implements edges
    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    ) as JsonLdGraph;

    // Check for function nodes
    const functionNodes = jsonldContent['@graph'].filter(
      (item: JsonLdNode) => item['@type'] === 'function'
    );
    expect(functionNodes).toHaveLength(2);

    // Check for component nodes
    const componentNodes = jsonldContent['@graph'].filter(
      (item: JsonLdNode) => item['@type'] === 'Component'
    );
    expect(componentNodes).toHaveLength(2);

    // Check for implements relationships
    const implementsRels = jsonldContent['@graph'].filter(
      (item: JsonLdNode): item is JsonLdRelationship =>
        item['@type'] === 'Relationship' &&
        (item as JsonLdRelationship).relationship === 'implements'
    );
    expect(implementsRels).toHaveLength(2);

    // Verify relationship structure
    const parseAnnotationsRel = implementsRels.find((rel) =>
      rel.source?.includes('parseAnnotations')
    );
    expect(parseAnnotationsRel).toBeDefined();
    expect(parseAnnotationsRel?.target).toContain('AnnotationParser');
    expect(parseAnnotationsRel?.confidence).toBe(0.9);
  });

  it('should handle file path matching for annotations', async () => {
    // Test relative vs absolute file path matching
    const mockAnnotations = [
      {
        milestoneId: 'M1.2',
        componentName: 'TestComponent',
        functionName: 'testFunction',
        filePath: 'utils.ts', // Relative path
        confidence: 0.9,
        lastVerified: '2025-01-01T00:00:00Z',
        lineNumber: 5,
        errors: [],
      },
    ];

    const mockCodeParseResult = {
      functions: [
        {
          id: 'function:abcdef12',
          name: 'testFunction',
          signature: 'testFunction(): void',
          file: 'src/utils.ts', // Absolute-ish path
          line_start: 5,
          line_end: 10,
          lang: 'typescript' as const,
        },
      ],
      calls: [],
      errors: [],
      stats: { filesProcessed: 1, functionsFound: 1, callsFound: 0 },
    };

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
      annotations: mockAnnotations,
      codeParseResult: mockCodeParseResult,
    });

    expect(result.errors).toHaveLength(0);

    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    ) as JsonLdGraph;

    // Should create implements edge despite path mismatch (endsWith matching)
    const implementsRels = jsonldContent['@graph'].filter(
      (item: JsonLdNode): item is JsonLdRelationship =>
        item['@type'] === 'Relationship' &&
        (item as JsonLdRelationship).relationship === 'implements'
    );
    expect(implementsRels).toHaveLength(1);
  });

  it('should handle annotations without matching functions gracefully', async () => {
    // Test annotations that don't match any functions
    const mockAnnotations = [
      {
        milestoneId: 'M1.2',
        componentName: 'NonExistentComponent',
        functionName: 'nonExistentFunction',
        filePath: 'missing.ts',
        confidence: 0.9,
        lastVerified: '2025-01-01T00:00:00Z',
        lineNumber: 1,
        errors: [],
      },
    ];

    const mockCodeParseResult = {
      functions: [
        {
          id: 'function:fedcba98',
          name: 'differentFunction',
          signature: 'differentFunction(): void',
          file: 'different.ts',
          line_start: 1,
          line_end: 5,
          lang: 'typescript' as const,
        },
      ],
      calls: [],
      errors: [],
      stats: { filesProcessed: 1, functionsFound: 1, callsFound: 0 },
    };

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
      annotations: mockAnnotations,
      codeParseResult: mockCodeParseResult,
    });

    expect(result.errors).toHaveLength(0);

    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    ) as JsonLdGraph;

    // Should not create implements edges for non-matching annotations
    const implementsRels = jsonldContent['@graph'].filter(
      (item: JsonLdNode): item is JsonLdRelationship =>
        item['@type'] === 'Relationship' &&
        (item as JsonLdRelationship).relationship === 'implements'
    );
    expect(implementsRels).toHaveLength(0);

    // Should still have function nodes from code parsing
    const functionNodes = jsonldContent['@graph'].filter(
      (item: JsonLdNode) => item['@type'] === 'function'
    );
    expect(functionNodes).toHaveLength(1);
  });

  it('should handle single string values in relationships', async () => {
    const specContent = `---
title: Single Relationship Spec
implements: single-target
dependsOn: single-dependency
---

# Single Relationship Spec
`;

    writeFileSync(join(testDir, 'single-rel.mdx'), specContent);

    const result = await buildKnowledgeGraph(testDir, {
      dryRun: false,
      outputDir: testDir,
    });

    expect(result.summary.relationshipsCount).toBe(2); // 1 implements + 1 dependsOn

    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    ) as JsonLdGraph;
    const relationships = jsonldContent['@graph'].filter(
      (item: JsonLdNode): item is JsonLdRelationship =>
        item['@type'] === 'Relationship'
    );

    expect(relationships).toHaveLength(2);
    expect(
      relationships.some(
        (rel: JsonLdRelationship) => rel.target === 'single-target'
      )
    ).toBe(true);
    expect(
      relationships.some(
        (rel: JsonLdRelationship) => rel.target === 'single-dependency'
      )
    ).toBe(true);
  });
});

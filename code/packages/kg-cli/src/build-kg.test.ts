/**
 * @fileoverview Tests for build-kg command with annotation support
 */

import { writeFileSync, mkdirSync, rmSync, readFileSync } from 'fs';
import { join } from 'path';
import { buildKnowledgeGraph } from './index.js';
import { parseCodeDirectory } from '@workflow-mapper/code-parser-lib';

// Mock parseAnnotationsFromDirectory function
jest.mock('./build-kg.js', () => ({
  ...jest.requireActual('./build-kg.js'),
}));

describe('build-kg with annotation support', () => {
  const testDir = join(__dirname, '../test-build-kg');
  const specsDir = join(testDir, 'specs');
  const codeDir = join(testDir, 'code');

  beforeEach(() => {
    mkdirSync(testDir, { recursive: true });
    mkdirSync(specsDir, { recursive: true });
    mkdirSync(codeDir, { recursive: true });
  });

  afterEach(() => {
    rmSync(testDir, { recursive: true, force: true });
  });

  it('should parse TypeScript files and create implements edges', async () => {
    // Create a milestone spec
    const specContent = `---
title: "Test Milestone M1.2"
description: "Test milestone with components"
version: "1.0.0"
status: "Draft"
---

# Test Milestone M1.2

## Components

- AnnotationParser: Parses @implements annotations from source code
- ValidationEngine: Validates parsed annotations
`;

    writeFileSync(join(specsDir, 'milestone-M1.2.mdx'), specContent);

    // Create TypeScript files with @implements annotations
    const tsFileWithAnnotation = `/**
 * @fileoverview Annotation parsing functionality
 * @implements milestone-M1.2#AnnotationParser
 */

export function parseAnnotations(content: string): any[] {
  return [];
}

export function helperFunction(): void {
  // Helper function without annotation
}
`;

    const tsFileWithClassAnnotation = `/**
 * @implements milestone-M1.2#ValidationEngine
 */
export class ValidationEngine {
  validate(data: any): boolean {
    return true;
  }
}
`;

    writeFileSync(join(codeDir, 'parser.ts'), tsFileWithAnnotation);
    writeFileSync(join(codeDir, 'validator.ts'), tsFileWithClassAnnotation);

    // Parse code directory (this would normally be done by build-kg command)
    const codeParseResult = parseCodeDirectory(codeDir, ['ts']);

    // Simulate annotation parsing (normally done by parseAnnotationsFromDirectory)
    const mockAnnotations = [
      {
        milestoneId: 'M1.2',
        componentName: 'AnnotationParser',
        functionName: 'parseAnnotations',
        filePath: 'parser.ts',
        confidence: 0.9,
        lastVerified: new Date().toISOString(),
        lineNumber: 6,
        errors: [],
      },
      {
        milestoneId: 'M1.2',
        componentName: 'ValidationEngine',
        functionName: 'ValidationEngine',
        filePath: 'validator.ts',
        confidence: 0.9,
        lastVerified: new Date().toISOString(),
        lineNumber: 4,
        errors: [],
      },
    ];

    // Build knowledge graph with code and annotations
    const result = await buildKnowledgeGraph(specsDir, {
      dryRun: false,
      outputDir: testDir,
      codeParseResult,
      annotations: mockAnnotations,
    });

    expect(result.errors).toHaveLength(0);
    expect(result.summary.specsCount).toBe(1);
    expect(result.summary.functionsCount).toBeGreaterThan(0);

    // Verify the knowledge graph contains implements edges
    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    );

    const graph = jsonldContent['@graph'];

    // Check for function nodes
    const functionNodes = graph.filter(
      (node: any) => node['@type'] === 'function'
    );
    expect(functionNodes.length).toBeGreaterThan(0);

    // Check for component nodes
    const componentNodes = graph.filter(
      (node: any) => node['@type'] === 'Component'
    );
    expect(componentNodes.length).toBe(2);

    // Check for implements relationships
    const implementsRels = graph.filter(
      (item: any) =>
        item['@type'] === 'Relationship' && item.relationship === 'implements'
    );
    expect(implementsRels.length).toBe(2);

    // Verify specific relationships
    const annotationParserRel = implementsRels.find((rel: any) =>
      rel.target?.includes('AnnotationParser')
    );
    expect(annotationParserRel).toBeDefined();
    expect(annotationParserRel.confidence).toBe(0.9);

    const validationEngineRel = implementsRels.find((rel: any) =>
      rel.target?.includes('ValidationEngine')
    );
    expect(validationEngineRel).toBeDefined();
    expect(validationEngineRel.confidence).toBe(0.9);
  });

  it('should handle file-level annotations correctly', async () => {
    // Create TypeScript file with file-level annotation
    const tsFileContent = `/**
 * @fileoverview Main parser module
 * @implements milestone-M1.2#MainParser
 */

import { SomeType } from './types';

export function mainParseFunction(input: string): SomeType {
  return {} as SomeType;
}

function helperFunction(): void {
  // Internal helper
}
`;

    writeFileSync(join(codeDir, 'main-parser.ts'), tsFileContent);

    const codeParseResult = parseCodeDirectory(codeDir, ['ts']);

    // Mock annotation that should attach to the main exported function
    const mockAnnotations = [
      {
        milestoneId: 'M1.2',
        componentName: 'MainParser',
        functionName: 'mainParseFunction', // Should find this as the main exported function
        filePath: 'main-parser.ts',
        confidence: 0.9,
        lastVerified: new Date().toISOString(),
        lineNumber: 1,
        errors: [],
      },
    ];

    const result = await buildKnowledgeGraph(specsDir, {
      dryRun: false,
      outputDir: testDir,
      codeParseResult,
      annotations: mockAnnotations,
    });

    expect(result.errors).toHaveLength(0);

    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    );

    const implementsRels = jsonldContent['@graph'].filter(
      (item: any) =>
        item['@type'] === 'Relationship' && item.relationship === 'implements'
    );

    expect(implementsRels.length).toBe(1);
    expect(implementsRels[0].target).toContain('MainParser');
  });

  it('should handle missing function matches gracefully', async () => {
    // Create annotation that doesn't match any function
    const mockAnnotations = [
      {
        milestoneId: 'M1.2',
        componentName: 'NonExistentComponent',
        functionName: 'nonExistentFunction',
        filePath: 'missing.ts',
        confidence: 0.9,
        lastVerified: new Date().toISOString(),
        lineNumber: 1,
        errors: [],
      },
    ];

    // Create a real TypeScript file
    const tsFileContent = `export function realFunction(): void {}`;
    writeFileSync(join(codeDir, 'real.ts'), tsFileContent);

    const codeParseResult = parseCodeDirectory(codeDir, ['ts']);

    const result = await buildKnowledgeGraph(specsDir, {
      dryRun: false,
      outputDir: testDir,
      codeParseResult,
      annotations: mockAnnotations,
    });

    expect(result.errors).toHaveLength(0);

    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    );

    // Should have function nodes from code parsing
    const functionNodes = jsonldContent['@graph'].filter(
      (node: any) => node['@type'] === 'function'
    );
    expect(functionNodes.length).toBeGreaterThan(0);

    // Should not have implements relationships for non-matching annotations
    const implementsRels = jsonldContent['@graph'].filter(
      (item: any) =>
        item['@type'] === 'Relationship' && item.relationship === 'implements'
    );
    expect(implementsRels.length).toBe(0);
  });

  it('should handle empty annotations array', async () => {
    const tsFileContent = `export function someFunction(): void {}`;
    writeFileSync(join(codeDir, 'some.ts'), tsFileContent);

    const codeParseResult = parseCodeDirectory(codeDir, ['ts']);

    const result = await buildKnowledgeGraph(specsDir, {
      dryRun: false,
      outputDir: testDir,
      codeParseResult,
      annotations: [], // Empty annotations
    });

    expect(result.errors).toHaveLength(0);

    const jsonldContent = JSON.parse(
      readFileSync(join(testDir, 'kg.jsonld'), 'utf-8')
    );

    // Should have function nodes from code parsing
    const functionNodes = jsonldContent['@graph'].filter(
      (node: any) => node['@type'] === 'function'
    );
    expect(functionNodes.length).toBeGreaterThan(0);

    // Should not have any implements relationships
    const implementsRels = jsonldContent['@graph'].filter(
      (item: any) =>
        item['@type'] === 'Relationship' && item.relationship === 'implements'
    );
    expect(implementsRels.length).toBe(0);
  });
});

{"@context": {"@vocab": "https://workflow-mapper.dev/vocab#", "title": "https://schema.org/name", "description": "https://schema.org/description", "status": "https://workflow-mapper.dev/vocab#status", "version": "https://schema.org/version", "created": "https://schema.org/dateCreated", "updated": "https://schema.org/dateModified", "tags": "https://schema.org/keywords", "authors": "https://schema.org/author", "filePath": "https://workflow-mapper.dev/vocab#filePath", "implements": "https://workflow-mapper.dev/vocab#implements", "dependsOn": "https://workflow-mapper.dev/vocab#dependsOn", "contains": "https://workflow-mapper.dev/vocab#contains"}, "@graph": [{"@id": "spec--users-nitish-tmp-kloudi-swe-agent-code-packages-kg-cli-test-integration-specs-cli-test-mdx-cli-test", "@type": "Specification", "filePath": "/Users/<USER>/tmp/kloudi-swe-agent/code/packages/kg-cli/test-integration/specs/cli-test.mdx", "title": "CLI Test", "description": "CLI integration test", "version": "1.0.0", "status": "Draft"}, {"@id": "function:04ab5678", "@type": "function", "title": "cli_test", "description": "Function cli_test() in /Users/<USER>/tmp/kloudi-swe-agent/code/packages/kg-cli/test-integration/code/cli-test.py", "filePath": "/Users/<USER>/tmp/kloudi-swe-agent/code/packages/kg-cli/test-integration/code/cli-test.py", "name": "cli_test", "signature": "cli_test()", "lang": "python", "line_start": 2, "line_end": 3}]}
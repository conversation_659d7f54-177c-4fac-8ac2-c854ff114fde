{"timestamp": "2025-06-02T09:13:40.850Z", "summary": {"nodesAdded": 2, "nodesUpdated": 0, "nodesMarkedStale": 0, "edgesAdded": 1, "edgesUpdated": 0, "edgesMarkedStale": 0}, "coverage": [], "errors": [{"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}, {"message": "File name \"sync-kg.ts\" doesn't match component \"CLIIntegration\"", "severity": "warning", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}]}
# @workflow-mapper/kg-cli

Command-line interface for knowledge graph operations including specification parsing and bidirectional sync.

## Overview

This package provides CLI tools for building and maintaining knowledge graphs from MDX specifications and source code annotations. It supports both full graph generation and incremental synchronization with git diff integration.

### Key Features

- **Specification Parsing**: Build knowledge graphs from MDX milestone specifications
- **Bidirectional Sync**: Incremental updates with `@implements` annotation parsing
- **Git Integration**: Detect changed files for efficient incremental processing
- **Multiple Formats**: Output in JSON-LD and YAML formats
- **Coverage Metrics**: Calculate and validate milestone implementation coverage
- **Performance Optimization**: 90%+ improvement over full repository scans
- **CI/CD Integration**: Designed for automated validation workflows

## Installation

```bash
pnpm add @workflow-mapper/kg-cli
```

## Commands

### build-kg

Build complete knowledge graph from MDX specifications.

```bash
# Basic usage
pnpm run build-kg docs/tech-specs

# Dry-run validation
pnpm run build-kg --dry-run docs/tech-specs

# Custom output directory
pnpm run build-kg --output-dir ./output docs/tech-specs

# Verbose output
pnpm run build-kg --verbose docs/tech-specs
```

**Options:**
- `--dry-run`: Validate without writing files
- `--output-dir <dir>`: Specify output directory (default: current directory)
- `--verbose`: Enable detailed logging

**Output Files:**
- `kg.jsonld`: JSON-LD formatted knowledge graph
- `kg.yaml`: YAML formatted knowledge graph (human-readable)

### sync-kg

Incremental knowledge graph synchronization with git diff integration.

```bash
# Basic incremental sync
pnpm run sync-kg -- --since origin/main docs/tech-specs

# Dry-run validation
pnpm run sync-kg -- --since HEAD~1 --dry-run docs/tech-specs

# Custom coverage threshold
pnpm run sync-kg -- --since HEAD~5 --threshold 0.7 docs/tech-specs

# Verbose output with performance metrics
pnpm run sync-kg -- --since origin/main --verbose docs/tech-specs
```

**Options:**
- `--since <git-ref>`: Git reference for incremental diff (required)
- `--dry-run`: Validate without writing files
- `--threshold <number>`: Coverage threshold for validation (default: 0.5)
- `--output-dir <dir>`: Specify output directory (default: current directory)
- `--verbose`: Enable detailed logging

**Output Files:**
- `kg.jsonld`: Updated JSON-LD knowledge graph
- `kg.yaml`: Updated YAML knowledge graph
- `kg-changes.json`: Detailed change report with added/removed/modified items

**Exit Codes:**
- `0`: Success
- `60`: Coverage threshold breach (coverage < threshold)
- `70`: Parse errors in annotations
- `1`: General errors (git failures, file system issues)

## Annotation Format

The sync-kg command parses `@implements` annotations from source code:

```typescript
/**
 * @implements milestone-M1.2#ComponentName
 */
export function myFunction() {
  // Implementation
}

/**
 * @implements milestone-M1.2#GitDiffDetector
 */
class GitDiffDetector {
  /**
   * @implements milestone-M1.2#DiffAnalysis
   */
  async detectChanges(since: string): Promise<DiffResult> {
    // Implementation
  }
}
```

**Requirements:**
- Must be in JSDoc/TSDoc comment block
- Must be attached to function, class, or method
- Milestone ID must match pattern: `milestone-M\d+(\.\d+)*`
- Component name must be valid identifier: `[A-Za-z_][A-Za-z0-9_]*`

## Examples

### CI/CD Integration

```yaml
# .github/workflows/kg-sync.yml
name: Knowledge Graph Sync
on: pull_request

jobs:
  sync-validation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && cd code && pnpm install
      - run: cd code && pnpm build
      - run: cd code && pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs
```

### Local Development Workflow

```bash
# 1. Make code changes with annotations
echo '/**
 * @implements milestone-M1.2#NewFeature
 */
export function newFeature() {
  return "implemented";
}' > src/new-feature.ts

# 2. Run incremental sync
pnpm run sync-kg -- --since HEAD~1 docs/tech-specs

# 3. Check coverage report
cat kg-changes.json | jq '.coverage'
```

### Batch Processing

```bash
# Process multiple commits
for commit in $(git rev-list HEAD~10..HEAD); do
  echo "Processing commit: $commit"
  pnpm run sync-kg -- --since $commit~1 --dry-run docs/tech-specs
done
```

## Performance

The CLI tools are optimized for different use cases:

### build-kg Performance
- **Full Parsing**: Complete specification analysis
- **Caching**: Efficient re-parsing of unchanged files
- **Memory Usage**: Scales with specification size

### sync-kg Performance
- **Incremental Processing**: 90%+ improvement over full scans
- **Git Integration**: Fast diff detection using simple-git
- **Linear Scaling**: Performance scales with number of changed files
- **Memory Efficient**: <100MB for typical repositories

## Configuration

### Environment Variables

- `KG_CLI_DEBUG`: Enable debug logging
- `KG_SYNC_TIMEOUT`: Git operation timeout in milliseconds (default: 30000)
- `KG_OUTPUT_FORMAT`: Default output format preference

### Package Scripts

Add these scripts to your package.json:

```json
{
  "scripts": {
    "build-kg": "build-kg",
    "sync-kg": "sync-kg",
    "kg:validate": "build-kg --dry-run docs/tech-specs",
    "kg:sync": "sync-kg --since origin/main docs/tech-specs"
  }
}
```

## Development

```bash
# Install dependencies
pnpm install

# Build
pnpm build

# Run tests
pnpm test

# Type check
pnpm type-check
```

## License

ISC

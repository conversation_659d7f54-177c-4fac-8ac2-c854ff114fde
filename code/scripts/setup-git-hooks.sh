#!/bin/sh

# Script to set up Git hooks
echo "Setting up Git hooks..."

# Ensure Git uses standard hooks directory (not <PERSON>sky)
cd ..
git config --unset core.hookspath 2>/dev/null || true
cd code

# Create hooks directory if it doesn't exist
mkdir -p ../.git/hooks

# Create hooks directory for storing hooks
mkdir -p ../.github/hooks

# Create commit-msg hook if it doesn't exist in .github/hooks
if [ ! -f ../.github/hooks/commit-msg ]; then
    cat > ../.github/hooks/commit-msg << 'EOF'
#!/bin/sh
#
# Git commit-msg hook to enforce semantic versioning commit messages
# Platform and language agnostic solution
#

commit_file="$1"
commit_message=$(cat "$commit_file")

# Remove any leading/trailing whitespace
commit_message=$(echo "$commit_message" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# Check if commit message is empty
if [ -z "$commit_message" ]; then
    echo "❌ Commit message cannot be empty!"
    exit 1
fi

# Define valid commit types
valid_types="feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert"

# Check if commit message follows semantic format
if ! echo "$commit_message" | grep -Eq "^($valid_types)(\([a-zA-Z0-9._-]+\))?: .+$"; then
    echo "❌ Invalid commit message format!"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages must follow this format:"
    echo "   type(scope): description"
    echo ""
    echo "Valid types:"
    echo "   • feat     : A new feature"
    echo "   • fix      : A bug fix"
    echo "   • docs     : Documentation changes"
    echo "   • style    : Code style changes (formatting, etc)"
    echo "   • refactor : Code refactoring"
    echo "   • perf     : Performance improvements"
    echo "   • test     : Adding or fixing tests"
    echo "   • build    : Build system changes"
    echo "   • ci       : CI configuration changes"
    echo "   • chore    : Other changes"
    echo "   • revert   : Revert a previous commit"
    echo ""
    echo "Scope is optional. Description is required."
    echo "Scope can contain: letters, numbers, periods, underscores, hyphens"
    echo ""
    echo "Examples of good commit messages:"
    echo "   • feat(auth): add user authentication"
    echo "   • fix(M1.2): resolve parsing issue"
    echo "   • docs(user_guide): update installation guide"
    echo "   • refactor(api-service): simplify user service"
    echo "   • feat(kg-sync_lib): add annotation parser"
    echo ""
    exit 1
fi

# Check if commit message is too long (max 500 characters)
message_length=$(echo "$commit_message" | wc -c)
if [ "$message_length" -gt 500 ]; then
    echo "❌ Commit message is too long!"
    echo "📏 Current length: $message_length characters (max: 500)"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages should be:"
    echo "   • Maximum 500 characters"
    echo "   • Clear and concise"
    echo "   • Follow the format: type(scope): description"
    echo ""
    exit 1
fi

echo "✅ Commit message follows semantic convention and looks good! ($message_length chars)"
exit 0
EOF
    chmod +x ../.github/hooks/commit-msg
fi

# Create pre-commit hook for lint checking, test coverage, and CI simulation
# Always recreate to ensure latest version
cat > ../.github/hooks/pre-commit << 'EOF'
#!/bin/sh
#
# Git pre-commit hook to run lint check and test coverage on entire repository
# Ensures code quality standards and test coverage before commits
#

echo "🔍 Running pre-commit checks..."

# Change to the code directory where package.json is located
cd code

# Run lint check on entire codebase
echo "🔍 Running lint check on entire repository..."
if ! pnpm lint; then
    echo ""
    echo "❌ Lint check failed!"
    echo "🔧 Please fix the linting errors above before committing."
    echo "💡 You can run 'pnpm lint --fix' to automatically fix some issues."
    echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ Lint check passed! Code quality looks good."

# Check if there are any staged TypeScript/JavaScript files that might need tests
staged_files=$(git diff --cached --name-only --diff-filter=AM | grep -E '\.(ts|tsx|js|jsx)$' | grep -v '\.test\.' | grep -v '\.spec\.' | head -10)

if [ -n "$staged_files" ]; then
    echo "🧪 Running tests to ensure coverage for new/modified code..."

    # Run tests to ensure they pass
    if ! pnpm test --passWithNoTests; then
        echo ""
        echo "❌ Tests failed!"
        echo "🔧 Please fix the failing tests before committing."
        echo "💡 You can run 'pnpm test' to see detailed test results."
        echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
        echo ""
        exit 1
    fi

    echo "✅ Tests passed!"

    # Check for potential missing test coverage (advisory only)
    echo "📊 Checking for potential test coverage gaps..."
    echo "📝 Modified/new files that may need test coverage:"
    echo "$staged_files" | while read -r file; do
        # Check if there's a corresponding test file
        base_name=$(echo "$file" | sed 's/\.[^.]*$//')
        test_file1="${base_name}.test.ts"
        test_file2="${base_name}.test.js"
        test_file3="${base_name}.spec.ts"
        test_file4="${base_name}.spec.js"

        if [ ! -f "$test_file1" ] && [ ! -f "$test_file2" ] && [ ! -f "$test_file3" ] && [ ! -f "$test_file4" ]; then
            echo "   ⚠️  $file (no corresponding test file found)"
        fi
    done

    echo ""
    echo "💡 Consider adding tests for files marked with ⚠️  if they contain new functionality."
    echo "📚 Test files should be named: filename.test.ts or filename.spec.ts"
else
    echo "📝 No TypeScript/JavaScript files modified - skipping test coverage check."
fi

# CI Simulation - Run the same commands that CI runs
echo ""
echo "🔄 Running CI simulation to catch integration issues..."

# Check if we have the required directories and commands
if [ -d "../docs/tech-specs" ] && command -v git >/dev/null 2>&1; then
    echo "🔍 Simulating sync-kg CI command..."

    # Run the same sync-kg command that CI runs (dry-run to avoid side effects)
    if ! pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs 2>/dev/null; then
        echo ""
        echo "❌ CI simulation failed!"
        echo "🔧 The sync-kg command that CI runs is failing."
        echo "💡 This means your changes would cause CI to fail."
        echo "🧪 To debug: pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs"
        echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
        echo ""
        exit 1
    fi

    echo "✅ CI simulation passed!"
else
    echo "⚠️  Skipping CI simulation (missing docs/tech-specs or git)"
fi

echo "✅ All pre-commit checks passed!"
exit 0
EOF
chmod +x ../.github/hooks/pre-commit

# Copy hooks to git hooks directory
cp ../.github/hooks/commit-msg ../.git/hooks/
cp ../.github/hooks/pre-commit ../.git/hooks/

# Make hooks executable
chmod +x ../.git/hooks/commit-msg
chmod +x ../.git/hooks/pre-commit

echo "✅ Git hooks setup complete with CI simulation!"
echo "📋 Configured hooks:"
echo "   • pre-commit: Runs lint check and test coverage validation"
echo "   • commit-msg: Validates semantic versioning format"
echo ""
echo "🧪 Pre-commit hook features:"
echo "   • Lints entire repository for code quality"
echo "   • Runs uncached tests to ensure they pass"
echo "   • Checks for missing test files (advisory)"
echo "   • Provides guidance on test coverage gaps"
echo "   • Simulates CI workflow to catch integration issues"
echo "   • Runs sync-kg command that CI uses to validate changes"
echo ""
echo "💡 To bypass hooks (not recommended): git commit --no-verify"

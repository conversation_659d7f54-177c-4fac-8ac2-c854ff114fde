{"timestamp": "2025-06-02T08:22:27.382Z", "summary": {"nodesAdded": 4, "nodesUpdated": 0, "nodesMarkedStale": 0, "edgesAdded": 2, "edgesUpdated": 0, "edgesMarkedStale": 1}, "coverage": [], "errors": [{"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}, {"message": "File name \"sync-kg.ts\" doesn't match component \"CLIIntegration\"", "severity": "warning", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}, {"message": "File name \"parseAnnotations.ts\" doesn't match component \"AnnotationParser\"", "severity": "warning", "file": "code/packages/kg-sync-lib/src/parseAnnotations.ts", "line": 1}]}
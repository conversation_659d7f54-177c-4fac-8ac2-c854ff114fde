{"name": "workflow-mapper", "private": true, "packageManager": "pnpm@8.15.4", "scripts": {"dev:api": "pnpm --filter apps/api dev", "dev:web": "pnpm --filter apps/web dev", "build": "turbo run build", "build-kg": "node packages/kg-cli/dist/build-kg.js", "sync-kg": "node packages/kg-cli/dist/sync-kg.js", "spec-lint": "node scripts/spec-lint.mjs", "lint": "eslint . --ext .ts,.tsx", "test": "turbo run test", "test:coverage": "turbo run test:coverage", "type-check": "turbo run type-check", "agent:dry-run": "node scripts/agent-dry-run.mjs", "prepare": "husky install"}, "devDependencies": {"@types/jest": "29.5.12", "@types/node": "20.12.7", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "7.7.0", "@typescript-eslint/parser": "7.7.0", "@vitest/coverage-v8": "^3.1.4", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "gray-matter": "^4.0.3", "husky": "9.0.11", "jest": "29.7.0", "lint-staged": "15.2.2", "nodemon": "3.1.0", "prettier": "3.2.5", "supertest": "7.1.1", "ts-jest": "29.1.2", "tsup": "8.0.2", "turbo": "1.13.2", "typescript": "5.4.3", "vitest": "3.1.4"}}